<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard - Status Notification Preview</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #052F11 0%, #0a5a1f 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .welcome-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* Permanent Status Notifications - Minimalistic Design */
        .permanent-status-notification {
            background: #ffffff;
            border: 1px solid #e8f5e8;
            border-left: 4px solid #28a745;
            border-radius: 8px;
            margin: 20px auto;
            max-width: 100%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            position: relative;
            z-index: 999;
            overflow: hidden;
        }

        .permanent-status-notification.rejected {
            border-left-color: #dc3545;
            border-color: #fdf2f2;
        }

        .permanent-status-notification .notification-content {
            display: flex;
            align-items: center;
            padding: 20px 24px;
            gap: 16px;
        }

        .permanent-status-notification .notification-icon {
            flex-shrink: 0;
            width: 48px;
            height: 48px;
            background: #f8f9fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #28a745;
        }

        .permanent-status-notification.rejected .notification-icon {
            border-color: #dc3545;
        }

        .permanent-status-notification .notification-icon i {
            color: #28a745;
            font-size: 1.2rem;
        }

        .permanent-status-notification.rejected .notification-icon i {
            color: #dc3545;
        }

        .permanent-status-notification .notification-text {
            flex: 1;
            color: #2c3e50;
        }

        .permanent-status-notification .notification-text .status-title {
            color: #28a745;
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 4px;
            display: block;
        }

        .permanent-status-notification.rejected .notification-text .status-title {
            color: #dc3545;
        }

        .permanent-status-notification .notification-text .status-details {
            font-size: 0.9rem;
            margin-bottom: 8px;
            line-height: 1.4;
            color: #6c757d;
        }

        .permanent-status-notification .notification-text .status-info {
            font-size: 0.8rem;
            color: #adb5bd;
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .permanent-status-notification .notification-text .status-info .info-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .permanent-status-notification .notification-text .status-info .info-item i {
            font-size: 0.7rem;
            opacity: 0.7;
        }

        .permanent-status-notification .notification-badge {
            flex-shrink: 0;
            background: #28a745;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .permanent-status-notification.rejected .notification-badge {
            background: #dc3545;
        }

        .scholarship-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .scholarship-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .apply-btn {
            background: #ffc107;
            color: #212529;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }

        .demo-label {
            background: #17a2b8;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-bottom: 10px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div>
                <h2>🎓 St. Paul University Philippines</h2>
                <p style="margin: 0; opacity: 0.9;">Office of the Registrar</p>
            </div>
            <button style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 8px 16px; border-radius: 4px;">Logout</button>
        </div>

        <div class="welcome-section">
            <div>
                <h3 style="margin: 0 0 5px 0;">Welcome, Jhon Danver Abogado!</h3>
                <p style="margin: 0; color: #6c757d;">Student ID: 2023-01401</p>
            </div>
            <div>
                <button style="background: #052F11; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px;">
                    <i class="fas fa-search"></i> Track Application
                </button>
                <button style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px;">
                    <i class="fas fa-cog"></i> Settings
                </button>
            </div>
        </div>

        <div class="demo-label">✨ APPROVED STATUS EXAMPLE</div>
        <!-- Approved Status Notification -->
        <div class="permanent-status-notification">
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="fas fa-check"></i>
                </div>
                <div class="notification-text">
                    <span class="status-title">Scholarship Approved</span>
                    <div class="status-details">
                        Your Academic scholarship application has been approved. Awarded: PL Scholarship
                    </div>
                    <div class="status-info">
                        <div class="info-item">
                            <i class="fas fa-hashtag"></i>
                            <span>SCH-AB1234</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span>Dec 15, 2024</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-user-graduate"></i>
                            <span>Active Scholar</span>
                        </div>
                    </div>
                </div>
                <div class="notification-badge">APPROVED</div>
            </div>
        </div>

        <div class="demo-label">❌ REJECTED STATUS EXAMPLE</div>
        <!-- Rejected Status Notification -->
        <div class="permanent-status-notification rejected">
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="fas fa-times"></i>
                </div>
                <div class="notification-text">
                    <span class="status-title">Application Rejected</span>
                    <div class="status-details">
                        Your Government scholarship application was not approved.
                    </div>
                    <div class="status-info">
                        <div class="info-item">
                            <i class="fas fa-hashtag"></i>
                            <span>SCH-CD5678</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span>Dec 10, 2024</span>
                        </div>
                    </div>
                </div>
                <div class="notification-badge">REJECTED</div>
            </div>
        </div>

        <div style="background: #052F11; color: white; padding: 15px; border-radius: 8px; margin-top: 20px;">
            <h3 style="margin: 0 0 10px 0;"><i class="fas fa-graduation-cap"></i> Scholarship Opportunities</h3>
            <div class="scholarship-grid">
                <div class="scholarship-card">
                    <h4>Government Scholarship</h4>
                    <p>Government scholarship for qualified students.</p>
                    <button class="apply-btn"><i class="fas fa-paper-plane"></i> Apply Now</button>
                </div>
                <div class="scholarship-card">
                    <h4>Academic Scholarship</h4>
                    <p>For students with exceptional academic performance.</p>
                    <button class="apply-btn"><i class="fas fa-paper-plane"></i> Apply Now</button>
                </div>
                <div class="scholarship-card">
                    <h4>Employee's Scholarship</h4>
                    <p>For children of university employees.</p>
                    <button class="apply-btn"><i class="fas fa-paper-plane"></i> Apply Now</button>
                </div>
                <div class="scholarship-card">
                    <h4>Private Scholarship</h4>
                    <p>For students with private scholarship opportunities.</p>
                    <button class="apply-btn"><i class="fas fa-paper-plane"></i> Apply Now</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
