<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'Admin Dashboard'); ?> - Scholarship Management</title>

    <!-- Base CSS Files -->
    <link rel="stylesheet" href="<?php echo e(asset('css/admin.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/admin-components.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/components/modals.css')); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Page-specific CSS -->
    <?php echo $__env->yieldPushContent('styles'); ?>

    <!-- Additional CSS from sections -->
    <?php echo $__env->yieldContent('additional-css'); ?>
</head>

<body>
    <!-- University Header -->
    <header class="university-header">
        <div class="header-content">
            <div class="university-logo-title">
                <img src="<?php echo e(asset('images/5x5 ft_LOGO.png')); ?>" alt="St. Paul University Philippines Logo"
                    class="university-logo">
                <div class="university-title">
                    <h1>St. Paul University Philippines</h1>
                    <h2>ADMINISTRATOR DASHBOARD</h2>
                </div>
            </div>
            <div class="user-actions">
                <a href="<?php echo e(route('login')); ?>" class="logout-btn">
                    <i class="fas fa-sign-out"></i> Logout
                </a>
            </div>
        </div>
    </header>

    <!-- Dashboard Banner -->
    <div class="dashboard-banner">
        <div class="banner-container">
            <div class="banner-content">
                <h2>SCHOLARSHIP MONITORING SYSTEM</h2>
                <?php echo $__env->yieldContent('breadcrumbs'); ?>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="admin-sidebar">
            <div class="admin-profile">
                <div class="profile-image">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="profile-info">
                    <h3>Admin User</h3>
                    <p>Administrator</p>
                </div>
            </div>
            <nav class="admin-nav">
                <a href="<?php echo e(route('admin.dashboard')); ?>"
                    class="nav-item <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="<?php echo e(route('admin.applications')); ?>"
                    class="nav-item <?php echo e(request()->routeIs('admin.applications*') ? 'active' : ''); ?>">
                    <i class="fas fa-graduation-cap"></i> Applications
                </a>
                <a href="<?php echo e(route('admin.students')); ?>"
                    class="nav-item <?php echo e(request()->routeIs('admin.students*') ? 'active' : ''); ?>">
                    <i class="fas fa-users"></i> Grantee
                </a>
                <a href="<?php echo e(route('admin.scholarships')); ?>"
                    class="nav-item <?php echo e(request()->routeIs('admin.scholarships*') ? 'active' : ''); ?>">
                    <i class="fas fa-award"></i> Benefactor
                </a>
                <a href="<?php echo e(route('admin.announcements')); ?>"
                    class="nav-item <?php echo e(request()->routeIs('admin.announcements*') ? 'active' : ''); ?>">
                    <i class="fas fa-bullhorn"></i> Announcements
                </a>
                <a href="<?php echo e(route('admin.archived-students')); ?>"
                    class="nav-item <?php echo e(request()->routeIs('admin.archived-students*') ? 'active' : ''); ?>">
                    <i class="fas fa-archive"></i> Archives
                </a>
                <a href="<?php echo e(route('admin.reports')); ?>"
                    class="nav-item <?php echo e(request()->routeIs('admin.reports*') ? 'active' : ''); ?>">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
                <a href="<?php echo e(route('admin.student-register')); ?>"
                    class="nav-item <?php echo e(request()->routeIs('admin.student-register*') ? 'active' : ''); ?>">
                    <i class="fas fa-users-cog"></i> Users Management
                </a>
                <a href="<?php echo e(route('admin.settings')); ?>"
                    class="nav-item <?php echo e(request()->routeIs('admin.settings*') ? 'active' : ''); ?>">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </nav>
        </div>

        <!-- Main Content Area -->
        <div class="admin-content">
            <?php echo $__env->yieldContent('content'); ?>
        </div>
    </div>

    <!-- Notification Component -->
    <?php echo $__env->make('components.notification', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Load jsPDF with multiple fallback options -->
    <script>
        // Global jsPDF loading function with multiple CDN sources
        window.loadJsPDF = function() {
            return new Promise((resolve, reject) => {
                if (typeof window.jsPDF !== 'undefined') {
                    console.log('jsPDF already loaded');
                    resolve();
                    return;
                }

                const cdnSources = [
                    'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js',
                    'https://unpkg.com/jspdf@2.5.1/dist/jspdf.umd.min.js',
                    'https://cdn.jsdelivr.net/npm/jspdf@2.5.1/dist/jspdf.umd.min.js',
                    'https://cdn.skypack.dev/jspdf@2.5.1'
                ];

                let currentIndex = 0;

                function tryLoadFromCDN() {
                    if (currentIndex >= cdnSources.length) {
                        console.error('All jsPDF CDN sources failed');
                        reject(new Error('Failed to load jsPDF from all CDN sources'));
                        return;
                    }

                    const script = document.createElement('script');
                    script.src = cdnSources[currentIndex];

                    script.onload = function() {
                        console.log(`jsPDF loaded successfully from: ${cdnSources[currentIndex]}`);
                        // Verify jsPDF is actually available
                        setTimeout(() => {
                            if (typeof window.jsPDF !== 'undefined') {
                                window.jsPDFLoaded = true;
                                resolve();
                            } else {
                                console.warn(
                                    'jsPDF script loaded but object not available, trying next CDN...'
                                    );
                                currentIndex++;
                                tryLoadFromCDN();
                            }
                        }, 100);
                    };

                    script.onerror = function() {
                        console.warn(`Failed to load jsPDF from: ${cdnSources[currentIndex]}`);
                        currentIndex++;
                        tryLoadFromCDN();
                    };

                    document.head.appendChild(script);
                }

                tryLoadFromCDN();
            });
        };

        // Try to load jsPDF immediately
        window.loadJsPDF().catch(error => {
            console.error('Initial jsPDF load failed:', error);
            window.jsPDFLoadError = true;
        });
    </script>

    <!-- Custom Confirm Dialog -->
    <script src="<?php echo e(asset('js/custom-confirm.js')); ?>"></script>
    <script src="<?php echo e(asset('js/error-handler.js')); ?>"></script>

    <!-- Override default browser dialogs to prevent "127.0.0.1:8000 says" -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Override alert() to use custom dialog
            window.originalAlert = window.alert;
            window.alert = function(message) {
                if (window.customConfirm) {
                    return window.customConfirm(message, 'Notice', 'info');
                } else {
                    // Fallback to original alert if custom confirm not available
                    return window.originalAlert(message);
                }
            };

            // Override confirm() to use custom dialog
            window.originalConfirm = window.confirm;
            window.confirm = function(message) {
                if (window.customConfirm) {
                    return window.customConfirm(message, 'Confirm', 'warning');
                } else {
                    // Fallback to original confirm if custom confirm not available
                    return window.originalConfirm(message);
                }
            };

            // Replace any existing confirm dialogs
            if (window.replaceConfirmDialogs) {
                window.replaceConfirmDialogs();
            }
        });
    </script>

    <!-- Base JavaScript -->
    <script>
        // Initialize navigation to ensure proper link behavior
        document.addEventListener('DOMContentLoaded', function() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // Allow default link behavior
                    return true;
                });
            });
        });
    </script>

    <!-- Page-specific JavaScript -->
    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- Additional JavaScript from sections -->
    <?php echo $__env->yieldContent('additional-js'); ?>
</body>

</html>
<?php /**PATH C:\Users\<USER>\OneDrive - St. Paul University Philippines\Desktop\CAPSTONE\System\Thesis\resources\views/layouts/admin.blade.php ENDPATH**/ ?>