<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['items' => []]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['items' => []]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<nav class="breadcrumb-nav" aria-label="Breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="<?php echo e(route('admin.dashboard')); ?>" class="breadcrumb-link">
                <i class="fas fa-home"></i>
                <span>Dashboard</span>
            </a>
        </li>

        <?php if(!empty($items)): ?>
            <li class="breadcrumb-separator">
                <i class="fas fa-chevron-right"></i>
            </li>
        <?php endif; ?>

        <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($loop->last): ?>
                <li class="breadcrumb-item active" aria-current="page">
                    <?php if(isset($item['icon'])): ?>
                        <i class="<?php echo e($item['icon']); ?>"></i>
                    <?php endif; ?>
                    <span><?php echo e($item['title']); ?></span>
                </li>
            <?php else: ?>
                <li class="breadcrumb-item">
                    <a href="<?php echo e($item['url']); ?>" class="breadcrumb-link">
                        <?php if(isset($item['icon'])): ?>
                            <i class="<?php echo e($item['icon']); ?>"></i>
                        <?php endif; ?>
                        <span><?php echo e($item['title']); ?></span>
                    </a>
                </li>
                <li class="breadcrumb-separator">
                    <i class="fas fa-chevron-right"></i>
                </li>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ol>
</nav>
<?php /**PATH C:\Users\<USER>\OneDrive - St. Paul University Philippines\Desktop\CAPSTONE\System\Thesis\resources\views/components/breadcrumb.blade.php ENDPATH**/ ?>